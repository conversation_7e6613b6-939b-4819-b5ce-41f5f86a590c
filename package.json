{"name": "llamacoder-new", "version": "0.1.0", "private": true, "scripts": {"postinstall": "prisma generate", "dev": "next dev", "build": "prisma generate && prisma migrate deploy && next build", "start": "next start", "lint": "next lint && tsc --noEmit --noUnusedLocals"}, "dependencies": {"@ai-sdk/groq": "^2.0.17", "@ai-sdk/react": "^2.0.33", "@codesandbox/sandpack-react": "^2.20.0", "@codesandbox/sandpack-themes": "^2.0.21", "@conform-to/zod": "^1.2.2", "@headlessui/react": "^2.2.0", "@neondatabase/serverless": "^1.0.1", "@prisma/adapter-neon": "6.5.0", "@prisma/client": "6.5.0", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@radix-ui/react-visually-hidden": "^1.2.3", "@tailwindcss/typography": "^0.5.15", "@vercel/og": "^0.6.4", "ai": "^5.0.33", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dedent": "^1.6.0", "framer-motion": "^11.18.0", "lucide-react": "^0.468.0", "next": "15.5.0", "next-s3-upload": "^0.3.4", "openai": "^5.20.0", "react": "^19", "react-dom": "^19", "react-markdown": "^9.0.3", "shiki": "^3.9.2", "sonner": "^2.0.7", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-stick-to-bottom": "^1.1.1", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^8", "eslint-config-next": "15.1.4", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.10", "prisma": "6.5.0", "tailwindcss": "^3.4.17", "typescript": "^5"}}